import 'package:flutter_test/flutter_test.dart';
import 'package:novel_app/models/novel.dart';

void main() {
  group('创建空白章节测试', () {

    test('应该能够创建新章节对象', () {
      // 创建一个新章节
      final newChapter = Chapter(
        number: 2,
        title: '第二章',
        content: '请在此输入章节内容...',
      );

      // 验证章节属性
      expect(newChapter.number, equals(2));
      expect(newChapter.title, equals('第二章'));
      expect(newChapter.content, equals('请在此输入章节内容...'));
    });

    test('应该能够向小说添加章节', () {
      // 创建一个测试小说
      final testNovel = Novel(
        title: '测试小说',
        genre: '科幻',
        outline: '测试大纲',
        content: '测试内容',
        chapters: [
          Chapter(number: 0, title: '大纲', content: '大纲内容'),
          Chapter(number: 1, title: '第一章', content: '第一章内容'),
        ],
        createdAt: DateTime.now(),
      );

      // 创建新章节
      final newChapter = Chapter(
        number: 2,
        title: '第二章',
        content: '请在此输入章节内容...',
      );

      // 添加到小说中
      testNovel.chapters.add(newChapter);
      testNovel.chapters.sort((a, b) => a.number.compareTo(b.number));

      // 验证章节已添加
      expect(testNovel.chapters.length, equals(3));

      final addedChapter = testNovel.chapters.firstWhere(
        (c) => c.number == 2,
      );

      expect(addedChapter.title, equals('第二章'));
      expect(addedChapter.content, equals('请在此输入章节内容...'));
    });
  });
}

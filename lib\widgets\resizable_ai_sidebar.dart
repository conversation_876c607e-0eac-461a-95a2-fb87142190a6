import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/screens/ai_chat/daizong_ai_screen.dart';
import 'package:novel_app/controllers/novel_controller.dart';
import 'package:novel_app/models/novel.dart';

class ResizableAISidebar extends StatefulWidget {
  final Widget child;
  final double initialSidebarWidth;
  final double minSidebarWidth;
  final double maxSidebarWidth;
  final bool showSidebar;
  final VoidCallback? onToggleSidebar;

  const ResizableAISidebar({
    super.key,
    required this.child,
    this.initialSidebarWidth = 400,
    this.minSidebarWidth = 300,
    this.maxSidebarWidth = 600,
    this.showSidebar = false,
    this.onToggleSidebar,
  });

  @override
  State<ResizableAISidebar> createState() => _ResizableAISidebarState();
}

class _ResizableAISidebarState extends State<ResizableAISidebar> {
  late double _sidebarWidth;
  bool _isResizing = false;

  @override
  void initState() {
    super.initState();
    _sidebarWidth = widget.initialSidebarWidth;
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // 主内容区域
        Expanded(
          child: widget.child,
        ),
        
        // 侧边栏
        if (widget.showSidebar) ...[
          // 分隔线和拖拽手柄
          MouseRegion(
            cursor: SystemMouseCursors.resizeColumn,
            child: GestureDetector(
              onPanStart: (details) {
                setState(() {
                  _isResizing = true;
                });
              },
              onPanUpdate: (details) {
                setState(() {
                  _sidebarWidth = (_sidebarWidth - details.delta.dx)
                      .clamp(widget.minSidebarWidth, widget.maxSidebarWidth);
                });
              },
              onPanEnd: (details) {
                setState(() {
                  _isResizing = false;
                });
              },
              child: Container(
                width: 8,
                color: _isResizing 
                    ? Theme.of(context).colorScheme.primary.withOpacity(0.3)
                    : Colors.transparent,
                child: Center(
                  child: Container(
                    width: 2,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Theme.of(context).dividerColor,
                      borderRadius: BorderRadius.circular(1),
                    ),
                  ),
                ),
              ),
            ),
          ),
          
          // AI助手侧边栏
          Container(
            width: _sidebarWidth,
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              border: Border(
                left: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 1,
                ),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(-2, 0),
                ),
              ],
            ),
            child: Column(
              children: [
                // 侧边栏标题栏
                Container(
                  height: 56,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    border: Border(
                      bottom: BorderSide(
                        color: Theme.of(context).dividerColor,
                        width: 1,
                      ),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.smart_toy,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '岱宗AI助手',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: widget.onToggleSidebar,
                        tooltip: '关闭AI助手',
                        iconSize: 20,
                      ),
                    ],
                  ),
                ),
                
                // AI助手内容
                Expanded(
                  child: _buildAIAssistantContent(),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildAIAssistantContent() {
    // 获取或创建岱宗AI助手控制器
    final controller = Get.put(DaizongAIController(), permanent: true);
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Column(
      children: [
        // 模式选择器（简化版）
        Container(
          padding: const EdgeInsets.all(12),
          child: Obx(() => SegmentedButton<String>(
                segments: const [
                  ButtonSegment<String>(
                    value: 'normal',
                    label: Text('普通', style: TextStyle(fontSize: 12)),
                    icon: Icon(Icons.chat_bubble_outline, size: 16),
                  ),
                  ButtonSegment<String>(
                    value: 'novel',
                    label: Text('小说', style: TextStyle(fontSize: 12)),
                    icon: Icon(Icons.book_outlined, size: 16),
                  ),
                ],
                selected: {controller.chatMode.value},
                onSelectionChanged: (Set<String> selection) {
                  controller.switchChatMode(selection.first);
                },
                style: ButtonStyle(
                  backgroundColor: WidgetStateProperty.resolveWith<Color>(
                    (states) {
                      if (states.contains(WidgetState.selected)) {
                        return Theme.of(context).colorScheme.primary;
                      }
                      return Theme.of(context).colorScheme.surface;
                    },
                  ),
                  foregroundColor: WidgetStateProperty.resolveWith<Color>(
                    (states) {
                      if (states.contains(WidgetState.selected)) {
                        return Theme.of(context).colorScheme.onPrimary;
                      }
                      return Theme.of(context).colorScheme.onSurface;
                    },
                  ),
                ),
              )),
        ),
            
            // 小说选择器（如果是小说模式）
            Obx(() => controller.chatMode.value == 'novel'
                ? Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    child: _buildCompactNovelSelector(controller),
                  )
                : const SizedBox.shrink()),
            
            // 消息列表
            Expanded(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 8),
                child: Obx(() => ListView.builder(
                      controller: controller.scrollController,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      itemCount: controller.messages.length,
                      itemBuilder: (context, index) {
                        final message = controller.messages[index];
                        return _buildCompactMessageBubble(message, isDark);
                      },
                    )),
              ),
            ),
            
            // 输入框
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(
                    color: Theme.of(context).dividerColor,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: controller.textController,
                      maxLines: 3,
                      minLines: 1,
                      textInputAction: TextInputAction.send,
                      decoration: InputDecoration(
                        hintText: '输入消息...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        isDense: true,
                      ),
                      style: const TextStyle(fontSize: 14),
                      onSubmitted: (value) {
                        if (value.trim().isNotEmpty) {
                          controller.sendMessage(value);
                        }
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  Obx(() => IconButton(
                        onPressed: controller.isGenerating.value
                            ? null
                            : () => controller.sendMessage(controller.textController.text),
                        icon: controller.isGenerating.value
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              )
                            : const Icon(Icons.send, size: 20),
                        style: IconButton.styleFrom(
                          backgroundColor: Theme.of(context).colorScheme.primary,
                          foregroundColor: Theme.of(context).colorScheme.onPrimary,
                          padding: const EdgeInsets.all(8),
                        ),
                      )),
                ],
              ),
            ),
          ],
        );
  }

  Widget _buildCompactNovelSelector(DaizongAIController controller) {
    final novels = Get.find<NovelController>().novels;
    
    return DropdownButtonFormField<Novel>(
      value: controller.selectedNovel.value,
      decoration: const InputDecoration(
        labelText: '选择小说',
        border: OutlineInputBorder(),
        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        isDense: true,
      ),
      items: novels.map((novel) {
        return DropdownMenuItem<Novel>(
          value: novel,
          child: Text(
            novel.title,
            style: const TextStyle(fontSize: 14),
            overflow: TextOverflow.ellipsis,
          ),
        );
      }).toList(),
      onChanged: (novel) {
        controller.selectNovel(novel);
      },
    );
  }

  Widget _buildCompactMessageBubble(ChatMessage message, bool isDark) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!message.isUser) ...[
            CircleAvatar(
              radius: 12,
              backgroundColor: Theme.of(context).colorScheme.primary,
              child: const Icon(Icons.smart_toy, size: 14, color: Colors.white),
            ),
            const SizedBox(width: 8),
          ],
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: message.isUser
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                message.content,
                style: TextStyle(
                  fontSize: 13,
                  color: message.isUser
                      ? Theme.of(context).colorScheme.onPrimary
                      : Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ),
          ),
          if (message.isUser) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              radius: 12,
              backgroundColor: Theme.of(context).colorScheme.secondary,
              child: const Icon(Icons.person, size: 14, color: Colors.white),
            ),
          ],
        ],
      ),
    );
  }
}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'package.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MembershipPackageAdapter extends TypeAdapter<MembershipPackage> {
  @override
  final int typeId = 26;

  @override
  MembershipPackage read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MembershipPackage(
      id: fields[0] as String,
      name: fields[1] as String,
      description: fields[2] as String,
      price: fields[3] as double,
      durationDays: fields[4] as int,
      features: (fields[5] as List).cast<String>(),
      limits: fields[6] as MembershipLimits,
      isActive: fields[7] as bool,
      sortOrder: fields[8] as int,
      displayOnly: fields[11] as bool,
      createdAt: fields[9] as DateTime,
      updatedAt: fields[10] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, MembershipPackage obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.price)
      ..writeByte(4)
      ..write(obj.durationDays)
      ..writeByte(5)
      ..write(obj.features)
      ..writeByte(6)
      ..write(obj.limits)
      ..writeByte(7)
      ..write(obj.isActive)
      ..writeByte(8)
      ..write(obj.sortOrder)
      ..writeByte(9)
      ..write(obj.createdAt)
      ..writeByte(10)
      ..write(obj.updatedAt)
      ..writeByte(11)
      ..write(obj.displayOnly);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MembershipPackageAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class MembershipLimitsAdapter extends TypeAdapter<MembershipLimits> {
  @override
  final int typeId = 27;

  @override
  MembershipLimits read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MembershipLimits(
      maxChaptersPerNovel: fields[0] as int,
      maxKnowledgeDocuments: fields[1] as int,
      canUseExtendedFeatures: fields[2] as bool,
      maxNovelsPerDay: fields[3] as int,
      maxWordsPerGeneration: fields[4] as int,
      canExportToMultipleFormats: fields[5] as bool,
      canUseAdvancedAI: fields[6] as bool,
      maxCustomCharacterTypes: fields[7] as int,
    );
  }

  @override
  void write(BinaryWriter writer, MembershipLimits obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.maxChaptersPerNovel)
      ..writeByte(1)
      ..write(obj.maxKnowledgeDocuments)
      ..writeByte(2)
      ..write(obj.canUseExtendedFeatures)
      ..writeByte(3)
      ..write(obj.maxNovelsPerDay)
      ..writeByte(4)
      ..write(obj.maxWordsPerGeneration)
      ..writeByte(5)
      ..write(obj.canExportToMultipleFormats)
      ..writeByte(6)
      ..write(obj.canUseAdvancedAI)
      ..writeByte(7)
      ..write(obj.maxCustomCharacterTypes);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MembershipLimitsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class MemberCodeAdapter extends TypeAdapter<MemberCode> {
  @override
  final int typeId = 28;

  @override
  MemberCode read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MemberCode(
      code: fields[0] as String,
      packageId: fields[1] as String,
      isUsed: fields[2] as bool,
      usedBy: fields[3] as String?,
      usedAt: fields[4] as DateTime?,
      createdAt: fields[5] as DateTime,
      expireAt: fields[6] as DateTime?,
      batchId: fields[7] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, MemberCode obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.code)
      ..writeByte(1)
      ..write(obj.packageId)
      ..writeByte(2)
      ..write(obj.isUsed)
      ..writeByte(3)
      ..write(obj.usedBy)
      ..writeByte(4)
      ..write(obj.usedAt)
      ..writeByte(5)
      ..write(obj.createdAt)
      ..writeByte(6)
      ..write(obj.expireAt)
      ..writeByte(7)
      ..write(obj.batchId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MemberCodeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MembershipPackage _$MembershipPackageFromJson(Map<String, dynamic> json) =>
    MembershipPackage(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      price: (json['price'] as num).toDouble(),
      durationDays: (json['durationDays'] as num).toInt(),
      features:
          (json['features'] as List<dynamic>).map((e) => e as String).toList(),
      limits: MembershipLimits.fromJson(json['limits'] as Map<String, dynamic>),
      isActive: json['isActive'] as bool? ?? true,
      sortOrder: (json['sortOrder'] as num?)?.toInt() ?? 0,
      displayOnly: json['displayOnly'] as bool? ?? false,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$MembershipPackageToJson(MembershipPackage instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'price': instance.price,
      'durationDays': instance.durationDays,
      'features': instance.features,
      'limits': instance.limits,
      'isActive': instance.isActive,
      'sortOrder': instance.sortOrder,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'displayOnly': instance.displayOnly,
    };

MembershipLimits _$MembershipLimitsFromJson(Map<String, dynamic> json) =>
    MembershipLimits(
      maxChaptersPerNovel: (json['maxChaptersPerNovel'] as num?)?.toInt() ?? -1,
      maxKnowledgeDocuments:
          (json['maxKnowledgeDocuments'] as num?)?.toInt() ?? 20,
      canUseExtendedFeatures: json['canUseExtendedFeatures'] as bool? ?? true,
      maxNovelsPerDay: (json['maxNovelsPerDay'] as num?)?.toInt() ?? -1,
      maxWordsPerGeneration:
          (json['maxWordsPerGeneration'] as num?)?.toInt() ?? -1,
      canExportToMultipleFormats:
          json['canExportToMultipleFormats'] as bool? ?? true,
      canUseAdvancedAI: json['canUseAdvancedAI'] as bool? ?? true,
      maxCustomCharacterTypes:
          (json['maxCustomCharacterTypes'] as num?)?.toInt() ?? -1,
    );

Map<String, dynamic> _$MembershipLimitsToJson(MembershipLimits instance) =>
    <String, dynamic>{
      'maxChaptersPerNovel': instance.maxChaptersPerNovel,
      'maxKnowledgeDocuments': instance.maxKnowledgeDocuments,
      'canUseExtendedFeatures': instance.canUseExtendedFeatures,
      'maxNovelsPerDay': instance.maxNovelsPerDay,
      'maxWordsPerGeneration': instance.maxWordsPerGeneration,
      'canExportToMultipleFormats': instance.canExportToMultipleFormats,
      'canUseAdvancedAI': instance.canUseAdvancedAI,
      'maxCustomCharacterTypes': instance.maxCustomCharacterTypes,
    };

MemberCode _$MemberCodeFromJson(Map<String, dynamic> json) => MemberCode(
      code: json['code'] as String,
      packageId: json['packageId'] as String,
      isUsed: json['isUsed'] as bool? ?? false,
      usedBy: json['usedBy'] as String?,
      usedAt: json['usedAt'] == null
          ? null
          : DateTime.parse(json['usedAt'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
      expireAt: json['expireAt'] == null
          ? null
          : DateTime.parse(json['expireAt'] as String),
      batchId: json['batchId'] as String?,
    );

Map<String, dynamic> _$MemberCodeToJson(MemberCode instance) =>
    <String, dynamic>{
      'code': instance.code,
      'packageId': instance.packageId,
      'isUsed': instance.isUsed,
      'usedBy': instance.usedBy,
      'usedAt': instance.usedAt?.toIso8601String(),
      'createdAt': instance.createdAt.toIso8601String(),
      'expireAt': instance.expireAt?.toIso8601String(),
      'batchId': instance.batchId,
    };
